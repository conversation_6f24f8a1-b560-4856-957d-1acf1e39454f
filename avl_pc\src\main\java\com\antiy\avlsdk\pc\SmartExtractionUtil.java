package com.antiy.avlsdk.pc;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

/**
 * 智能解压工具类
 * 
 * <h2>主要功能:</h2>
 * <ol>
 *     <li>基于 version.conf 文件的智能解压机制</li>
 *     <li>读取 zip 包内 version.conf 文件的时间戳</li>
 *     <li>与目标目录时间戳比较，决定是否需要解压</li>
 *     <li>提供目录完全清理功能</li>
 * </ol>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/24
 */
public class SmartExtractionUtil {
    private static final String TAG = "SmartExtractionUtil";
    private static final String VERSION_CONF_FILE = "version.conf";
    
    /**
     * 判断是否需要从 assets 解压
     *
     * @param context Android 上下文
     * @param zipName assets 中的 zip 文件名
     * @param targetDir 目标解压目录
  * @return true 表示需要解压，false 表示跳过解压过解压
     */
    public static boolean shouldExtractFromAssets(Context context, String zipName, String targetDir) {
        try {
            // 首先检查 assets 中的 zip 文件是否存在，如果不存在则无需解压
            try {
                context.getAssets().open(zipName).close();
            } catch (IOException e) {
                Log.w(TAG, "Assets zip file does not exist, no extraction needed: " + zipName);
                return false;
            }

            // 根据 zip 文件名推断具体的目标子目录
            String specificTargetDir = getSpecificTargetDirectoryFromZipName(zipName, targetDir);

            // 检查具体的目标子目录是否存在且有文件
            File targetDirectory = new File(specificTargetDir);
            if (!targetDirectory.exists() || !AssetsUtil.isFolderHasFiles(specificTargetDir)) {
                Log.i(TAG, "Specific target directory does not exist or is empty, extraction needed: " + specificTargetDir);
                return true;
            }
            
            // 读取 assets 中 zip 包的 version.conf 时间戳
            long zipVersionTimestamp = readVersionFromAssetsZip(context, zipName);
            if (zipVersionTimestamp == -1) {
                Log.w(TAG, "Cannot read version.conf from zip package, fallback to force extraction");
                return true;
            }
            
            // 获取目标目录的最后修改时间
            long targetDirTimestamp = targetDirectory.lastModified();
            
            Log.i(TAG, "Zip version.conf timestamp: " + zipVersionTimestamp +
                      ", target directory timestamp: " + targetDirTimestamp);
            
            // 比较时间戳
            return zipVersionTimestamp > targetDirTimestamp;
            
        } catch (Exception e) {
            Log.e(TAG, "Smart extraction check failed, fallback to force extraction", e);
            return true;
        }
    }
    
    /**
     * 判断是否需要从 vendor 解压
     *
     * 新逻辑：比较 zip 包内 version.conf 时间戳与本地 version.conf 文件时间戳
     * - 如果本地不存在 version.conf 文件，则需要解压（兼容旧版本）
     * - 如果 zip 包内 version.conf 时间戳 > 本地 version.conf 时间戳，则需要解压
     * - 否则跳过解压
     *
     * @param sourceZipPath vendor 中的 zip 文件路径
     * @param targetDir 目标解压目录
     * @return true 表示需要解压，false 表示跳过解压
     */
    public static boolean shouldExtractFromVendor(String sourceZipPath, String targetDir) {
        try {
            // 首先检查源 zip 文件是否存在，如果不存在则无需解压
            File sourceZipFile = new File(sourceZipPath);
            if (!sourceZipFile.exists()) {
                Log.w(TAG, "Source zip file does not exist, no extraction needed: " + sourceZipPath);
                return false;
            }

            // 根据 zip 文件名推断具体的目标子目录
            String specificTargetDir = getSpecificTargetDirectory(sourceZipPath, targetDir);

            // 优化：先检查目标目录状态，避免不必要的 zip 包读取操作
            File targetDirectory = new File(specificTargetDir);
            if (!targetDirectory.exists() || !AssetsUtil.isFolderHasFiles(specificTargetDir)) {
                Log.i(TAG, "Specific target directory does not exist or is empty, extraction needed: " + specificTargetDir);
                return true;
            }

            // 读取 vendor 中 zip 包的 version.conf 时间戳
            long zipVersionTimestamp = readVersionFromVendorZip(sourceZipPath);
            if (zipVersionTimestamp == -1) {
                // zip 包没有 version.conf 文件，采用智能回退策略
                Log.w(TAG, "Cannot read version.conf from zip package, using intelligent fallback strategy");
                return handleNoVersionConfCase(sourceZipFile, targetDirectory);
            }

            // 检查本地 version.conf 文件是否存在
            File localVersionFile = new File(specificTargetDir, VERSION_CONF_FILE);
            if (!localVersionFile.exists()) {
                Log.i(TAG, "Local version.conf file does not exist, extraction needed for compatibility with older versions");
                return true;
            }

            // 读取本地 version.conf 文件的时间戳
            long localVersionTimestamp = readVersionFromLocalFile(localVersionFile.getAbsolutePath());
            if (localVersionTimestamp == -1) {
                Log.w(TAG, "Cannot read local version.conf file, fallback to force extraction");
                return true;
            }

            Log.i(TAG, "Zip version.conf timestamp: " + zipVersionTimestamp +
                      ", local version.conf timestamp: " + localVersionTimestamp);

            // 比较时间戳：zip 包时间戳 > 本地文件时间戳时需要解压
            return zipVersionTimestamp > localVersionTimestamp;

        } catch (Exception e) {
            Log.e(TAG, "Smart extraction check failed, fallback to force extraction for safety", e);
            return true; // 修复：异常情况下强制解压，确保系统安全性
        }
    }
    
    /**
     * 从 assets 中的 zip 包读取 version.conf 文件的时间戳
     *
     * 注意：由于 Android Assets 的限制，必须使用遍历方式：
     * 1. AssetManager 只提供 InputStream 访问，不支持随机访问
     * 2. ZipInputStream 只能按顺序读取条目，无法直接跳转到指定文件
     * 3. 无法使用 ZipFile.getEntry() 方法，因为 assets 文件没有实际文件路径
     *
     * @param context Android 上下文
     * @param zipName zip 文件名
     * @return 时间戳，读取失败返回 -1
     */
    private static long readVersionFromAssetsZip(Context context, String zipName) {
        try {
            // 根据 zip 文件名构建 version.conf 的完整路径
            String versionConfPath = getVersionConfPathFromZipName(zipName);
            Log.i(TAG, "Looking for version.conf at: " + versionConfPath);

            AssetManager assetManager = context.getAssets();
            InputStream inputStream = assetManager.open(zipName);
            ZipInputStream zipInputStream = new ZipInputStream(inputStream);

            ZipEntry zipEntry;
            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                if (versionConfPath.equals(zipEntry.getName())) {
                    // 找到 version.conf 文件，读取内容
                    BufferedReader reader = new BufferedReader(new InputStreamReader(zipInputStream));
                    String timestampStr = reader.readLine();
                    if (timestampStr != null && !timestampStr.trim().isEmpty()) {
                        try {
                            long timestamp = Long.parseLong(timestampStr.trim());
                            Log.i(TAG, "Read version.conf timestamp from assets zip: " + timestamp);
                            return timestamp;
                        } catch (NumberFormatException e) {
                            Log.e(TAG, "Invalid version.conf timestamp format: " + timestampStr, e);
                        }
                    }
                    break;
                }
                zipInputStream.closeEntry();
            }
            
            zipInputStream.close();
            inputStream.close();
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to read version.conf from assets zip package", e);
        }
        
        return -1;
    }
    
    /**
     * 从 vendor 中的 zip 包读取 version.conf 文件的时间戳
     * 优化版本：使用 ZipFile 直接访问指定文件，避免遍历整个 ZIP 包
     *
     * @param sourceZipPath zip 文件路径
     * @return 时间戳，读取失败返回 -1
     */
    private static long readVersionFromVendorZip(String sourceZipPath) {
        try {
            // 根据 zip 文件名构建 version.conf 的完整路径
            String versionConfPath = getVersionConfPathFromZipPath(sourceZipPath);
            Log.i(TAG, "Looking for version.conf at: " + versionConfPath);

            File sourceZipFile = new File(sourceZipPath);

            // 使用 ZipFile 直接访问指定条目，避免遍历整个 ZIP 包
            try (ZipFile zipFile = new ZipFile(sourceZipFile)) {
                ZipEntry versionEntry = zipFile.getEntry(versionConfPath);

                if (versionEntry == null) {
                    Log.w(TAG, "File " + versionConfPath + " not found in zip package");
                    return -1;
                }

                // 直接读取 version.conf 文件内容
                try (InputStream inputStream = zipFile.getInputStream(versionEntry);
                     BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

                    String timestampStr = reader.readLine();
                    if (timestampStr != null && !timestampStr.trim().isEmpty()) {
                        try {
                            long timestamp = Long.parseLong(timestampStr.trim());
                            Log.i(TAG, "Read version.conf timestamp directly from vendor zip: " + timestamp);
                            return timestamp;
                        } catch (NumberFormatException e) {
                            Log.e(TAG, "Invalid version.conf timestamp format: " + timestampStr, e);
                        }
                    } else {
                        Log.w(TAG, "version.conf file content is empty");
                    }
                }
            }

        } catch (IOException e) {
            Log.e(TAG, "Failed to read version.conf from vendor zip package", e);
        }

        return -1;
    }

    /**
     * 从本地 version.conf 文件读取时间戳
     *
     * @param versionFilePath 本地 version.conf 文件的完整路径
     * @return 时间戳，读取失败返回 -1
     */
    private static long readVersionFromLocalFile(String versionFilePath) {
        try {
            File versionFile = new File(versionFilePath);
            if (!versionFile.exists()) {
                Log.w(TAG, "Local version.conf file does not exist: " + versionFilePath);
                return -1;
            }

            try (BufferedReader reader = new BufferedReader(new java.io.FileReader(versionFile))) {
                String timestampStr = reader.readLine();
                if (timestampStr != null && !timestampStr.trim().isEmpty()) {
                    try {
                        long timestamp = Long.parseLong(timestampStr.trim());
                        Log.i(TAG, "Read local version.conf timestamp: " + timestamp + " from file: " + versionFilePath);
                        return timestamp;
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "Invalid local version.conf timestamp format: " + timestampStr, e);
                    }
                } else {
                    Log.w(TAG, "Local version.conf file content is empty: " + versionFilePath);
                }
            }

        } catch (IOException e) {
            Log.e(TAG, "Failed to read local version.conf file: " + versionFilePath, e);
        }

        return -1;
    }

    /**
     * 处理 zip 包没有 version.conf 文件的情况
     * 智能回退策略：比较 zip 包修改时间与目标目录修改时间
     *
     * @param sourceZipFile 源 zip 文件对象文件对象
     * @param targetDirectory 目标目录对象
     * @return true 表示需要解压，false 表示跳过解压
     */
    private static boolean handleNoVersionConfCase(File sourceZipFile, File targetDirectory) {
        try {
          // 获取 zip 包的最后修改时间时间
            long zipFileTimestamp = sourceZipFile.lastModified();

            // 获取目标目录的最后修改时间
            long targetDirTimestamp = targetDirectory.lastModified();

            Log.i(TAG, "Fallback strategy - Zip file timestamp: " + zipFileTimestamp +
                      ", target directory timestamp: " + targetDirTimestamp);

            // 比较文件时间戳：如果 zip 包比目标目录新，则需要解压
            boolean needExtract = zipFileTimestamp > targetDirTimestamp;

            if (needExtract) {
                Log.i(TAG, "Zip package is newer than target directory, extraction needed");
            } else {
                Log.i(TAG, "Target directory is up-to-date, skip extraction to save resources");
            }

            return needExtract;

        } catch (Exception e) {
            Log.e(TAG, "Failed to compare timestamps in fallback strategy, force extraction for safety", e);
            return true; // 异常情况下仍然选择安全策略
        }
    }

    /**
     * 完全清理目标目录
     * 
     * @param targetDir 目标目录路径
     * @return true 表示清理成功，false 表示清理失败
     */
    public static boolean cleanTargetDirectory(String targetDir) {
        try {
            File directory = new File(targetDir);
            if (directory.exists()) {
                Log.i(TAG, "Start cleaning target directory: " + targetDir);
                deleteDirectory(directory);
                Log.i(TAG, "Target directory cleaning completed");
                return true;
            }
            return true; // 目录不存在，认为清理成功
        } catch (Exception e) {
            Log.e(TAG, "Failed to clean target directory: " + targetDir, e);
            return false;
        }
    }
    
    /**
     * 递归删除目录及其内容
     * 
     * @param directory 要删除的目录
     */
    private static void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }
    
    /**
     * 执行智能解压（从 assets）
     *
     * @param context Android 上下文
     * @param zipName zip 文件名
     * @param targetDir 目标目录
     * @throws IOException 解压失败时抛出
     */
    public static void smartExtractFromAssets(Context context, String zipName, String targetDir) throws IOException {
        if (shouldExtractFromAssets(context, zipName, targetDir)) {
            Log.i(TAG, "Extraction needed, start cleaning target directory and extracting");

            // 根据 zip 文件名推断要清理的具体子目录，避免误删其他目录
            String specificTargetDir = getSpecificTargetDirectoryFromZipName(zipName, targetDir);
            cleanTargetDirectory(specificTargetDir);

            AssetsUtil.unzipFromAssets(context, targetDir, zipName);
            Log.i(TAG, "Smart extraction completed");
        } else {
            Log.i(TAG, "Version not updated, skip extraction");
        }
    }
    
    /**
     * 执行智能解压（从 vendor）
     *
     * @param sourceZipPath 源 zip 文件路径
     * @param targetDir 目标目录
     * @throws IOException 解压失败时抛出
     */
    public static void smartExtractFromVendor(String sourceZipPath, String targetDir) throws IOException {
        if (shouldExtractFromVendor(sourceZipPath, targetDir)) {
            Log.i(TAG, "Extraction needed, start cleaning target directory and extracting");

            // 根据 zip 文件名推断要清理的具体子目录，避免误删其他目录
            String specificTargetDir = getSpecificTargetDirectory(sourceZipPath, targetDir);
            cleanTargetDirectory(specificTargetDir);

            VendorZipUtils.unzipFromVendor(sourceZipPath, targetDir);
            Log.i(TAG, "Smart extraction completed");
        } else {
            Log.i(TAG, "Version not updated, skip extraction");
        }
    }

    /**
     * 根据 zip 文件路径推断具体的目标子目录
     * 例如：avlsdk_mobile.zip -> /data/vids/avlsdk_mobile
     *      avlsdk_pc.zip -> /data/vids/avlsdk_pc
     *
     * @param sourceZipPath 源 zip 文件路径
     * @param targetDir 目标父目录
     * @return 具体的目标子目录路径
     */
    private static String getSpecificTargetDirectory(String sourceZipPath, String targetDir) {
        // 从 zip 文件路径中提取文件名（不含扩展名）
        File zipFile = new File(sourceZipPath);
        String zipFileName = zipFile.getName();

        // 移除.zip 扩展名
        if (zipFileName.endsWith(".zip")) {
            zipFileName = zipFileName.substring(0, zipFileName.length() - 4);
        }

        // 构建具体的目标子目录路径
        String specificTargetDir = targetDir + File.separator + zipFileName;
        Log.i(TAG, "Specific target directory: " + specificTargetDir + " (from zip: " + sourceZipPath + ")");

        return specificTargetDir;
    }

    /**
     * 根据 zip 文件名推断具体的目标子目录（用于 assets 解压）
     * 例如：avlsdk_mobile.zip -> /data/vids/avlsdk_mobile
     *      avlsdk_pc.zip -> /data/vids/avlsdk_pc
     *
     * @param zipName zip 文件名
     * @param targetDir 目标父目录
     * @return 具体的目标子目录路径
     */
    private static String getSpecificTargetDirectoryFromZipName(String zipName, String targetDir) {
        // 移除.zip 扩展名
        String dirName = zipName;
        if (dirName.endsWith(".zip")) {
            dirName = dirName.substring(0, dirName.length() - 4);
        }

        // 构建具体的目标子目录路径
        String specificTargetDir = targetDir + File.separator + dirName;
        Log.i(TAG, "Specific target directory: " + specificTargetDir + " (from zip name: " + zipName + ")");

        return specificTargetDir;
    }

    /**
     * 根据 zip 文件路径构建 version.conf 文件在 zip 包内的路径
     * 例如：avlsdk_mobile.zip -> avlsdk_mobile/version.conf
     *      avlsdk_pc.zip -> avlsdk_pc/version.conf
     *
     * @param sourceZipPath zip 文件路径
    * @return version.conf 在 zip 包内的路径径
     */
    private static String getVersionConfPathFromZipPath(String sourceZipPath) {
        // 从 zip 文件路径中提取文件名（不含扩展名）
        File zipFile = new File(sourceZipPath);
        String zipFileName = zipFile.getName();

        // 移除.zip 扩展名
        if (zipFileName.endsWith(".zip")) {
            zipFileName = zipFileName.substring(0, zipFileName.length() - 4);
        }

        // 构建 version.conf 在 zip 包内的路径
        return zipFileName + "/" + VERSION_CONF_FILE;
    }

    /**
     * 根据 zip 文件名构建 version.conf 文件在 zip 包内的路径（用于 assets）
     * 例如：avlsdk_mobile.zip -> avlsdk_mobile/version.conf
     *      avlsdk_pc.zip -> avlsdk_pc/version.conf
     *
     * @param zipName zip 文件名
     * @return version.conf 在 zip 包内的路径
     */
    private static String getVersionConfPathFromZipName(String zipName) {
        // 移除.zip 扩展名
        String dirName = zipName;
        if (dirName.endsWith(".zip")) {
            dirName = dirName.substring(0, dirName.length() - 4);
        }

        // 构建 version.conf 在 zip 包内的路径
        return dirName + "/" + VERSION_CONF_FILE;
    }
}
