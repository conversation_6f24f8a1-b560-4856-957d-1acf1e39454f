# 开发规范和规则

- base变体的AVLCoreEngine存在路径重复拼接问题：VIDS_DIR已包含TARGET_DIR但prepareData中又添加了一次，导致路径错误。需要修复为正确的私有目录路径。
- 已创建base版本的AVLEnginePC使用应用私有目录，解决Android 13权限问题
- 实现基于version.conf文件的智能解压机制：读取zip包内version.conf文件的13位毫秒级时间戳，与目标目录最后修改时间比较，如果version.conf时间戳更大则完全清理目标目录后重新解压
- 修改shouldExtractFromVendor函数逻辑：比较zip包内version.conf时间戳与本地version.conf文件时间戳，而非目录修改时间。本地version.conf不存在时强制解压以兼容旧版本。
- 修复shouldExtractFromVendor函数：1)优化执行顺序-先检查目标目录再读取zip包；2)统一错误处理策略-无法读取版本或异常时返回true强制解压；3)与shouldExtractFromAssets保持一致的安全优先策略
