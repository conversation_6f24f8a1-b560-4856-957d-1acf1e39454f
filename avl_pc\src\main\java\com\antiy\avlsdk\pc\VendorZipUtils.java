package com.antiy.avlsdk.pc;

import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 工具类：用于处理 ZIP 文件操作
 * 
 * <h2>主要功能:</h2>
 * <ol>
 *     <li>从系统/vendor/avlsdk 目录解压 ZIP 文件到应用私有目录</li>
 *     <li>提供安全的 ZIP 文件解压功能，防止 ZIP slip 攻击</li>
 * </ol>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/7
 */
public class VendorZipUtils {
    private static final String TAG = "VendorZipUtils";
    /**
     * 从/vendor/etc/idps/avlsdk/目录解压指定ZIP文件到应用私有目录
     * 
     * @param sourceZipPath 待解压的 ZIP 文件路径
     * @param targetDir 解压目标目录
     * @throws IOException 当文件操作失败时抛出
     */
    public static void unzipFromVendor(String sourceZipPath,String targetDir) throws IOException {
        File sourceZipFile = new File(sourceZipPath);
        File destDir = new File(targetDir);
        Log.i(TAG, "start copy engine from vendor ");
        if (!sourceZipFile.exists()) {
            Log.e(TAG, "Source zip file not found: " + sourceZipFile.getAbsolutePath());
        }
        long startTime  = System.currentTimeMillis();
        try (ZipInputStream zipInputStream = new ZipInputStream(Files.newInputStream(sourceZipFile.toPath()))) {
            ZipEntry zipEntry;
            byte[] buffer = new byte[1024];
            
            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                String fileName = zipEntry.getName();
                File newFile = new File(destDir, fileName);
                
                // 安全检查：确保解压的文件路径不会跑到目标目录之外（防止 ZIP slip 攻击）
                String canonicalDestinationPath = destDir.getCanonicalPath();
                String canonicalNewFilePath = newFile.getCanonicalPath();
                if (!canonicalNewFilePath.startsWith(canonicalDestinationPath)) {
                    Log.e(TAG, "Entry is outside of target directory: " + fileName);
                    return;
                }
                
                // 处理目录
                if (zipEntry.isDirectory()) {
                    if (!newFile.exists() && !newFile.mkdirs()) {
                        Log.e(TAG, "Failed to create directory: " + newFile.getAbsolutePath());
                    }
                    continue;
                }
                
                // 确保父目录存在
                File parent = newFile.getParentFile();
                if (!parent.exists() && !parent.mkdirs()) {
                    Log.e(TAG, "Failed to create directory: " + parent.getAbsolutePath());
                    return;
                }
                
                // 写入文件
                try (FileOutputStream fileOutputStream = new FileOutputStream(newFile)) {
                    int len;
                    while ((len = zipInputStream.read(buffer)) > 0) {
                        fileOutputStream.write(buffer, 0, len);
                    }
                }
                
                zipInputStream.closeEntry();
                Log.i(TAG, "copy + " + fileName +" success,path: " + newFile.getAbsolutePath());
            }
        }
        long endTime = System.currentTimeMillis();
        Log.i(TAG, "copy engine from vendor success,cost time:" + (endTime - startTime) + "ms");
    }
} 